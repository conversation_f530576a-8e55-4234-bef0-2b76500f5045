/**
 * Object-Oriented Force-Directed Graph Layout System
 * Implements Fruchterman-Reingold algorithm with adaptive parameters
 */

class Vector2D {
  constructor(x = 0, y = 0) {
    this.x = x;
    this.y = y;
  }

  add(otherVector) {
    this.x += otherVector.x;
    this.y += otherVector.y;
    return this;
  }

  subtract(otherVector) {
    this.x -= otherVector.x;
    this.y -= otherVector.y;
    return this;
  }

  multiply(scalar) {
    this.x *= scalar;
    this.y *= scalar;
    return this;
  }

  magnitude() {
    return Math.sqrt(this.x * this.x + this.y * this.y);
  }

  normalize() {
    const magnitude = this.magnitude();
    if (magnitude > 0) {
      this.x /= magnitude;
      this.y /= magnitude;
    }
    return this;
  }

  copy() {
    return new Vector2D(this.x, this.y);
  }

  static distance(vectorA, vectorB) {
    const deltaX = vectorA.x - vectorB.x;
    const deltaY = vectorA.y - vectorB.y;
    return Math.sqrt(deltaX * deltaX + deltaY * deltaY);
  }
}

class GraphNode {
  constructor(nodeId, initialPosition) {
    this.nodeId = nodeId;
    this.position = new Vector2D(initialPosition.x, initialPosition.y);
    this.originalPosition = new Vector2D(initialPosition.x, initialPosition.y);
    this.accumulatedForce = new Vector2D(0, 0);
    this.connectedNodeIds = new Set();
  }

  addConnection(connectedNodeId) {
    this.connectedNodeIds.add(connectedNodeId);
  }

  isConnectedTo(otherNodeId) {
    return this.connectedNodeIds.has(otherNodeId);
  }

  resetForces() {
    this.accumulatedForce = new Vector2D(0, 0);
  }

  applyForce(forceVector) {
    this.accumulatedForce.add(forceVector);
  }

  updatePosition(dampingFactor, maximumForce) {
    const dampedForce = this.accumulatedForce.copy().multiply(dampingFactor);
    const forceMagnitude = dampedForce.magnitude();

    if (forceMagnitude > maximumForce) {
      const scalingFactor = maximumForce / forceMagnitude;
      dampedForce.multiply(scalingFactor);
    }

    this.position.add(dampedForce);
  }
}

class GraphEdge {
  constructor(sourceNodeId, targetNodeId) {
    this.sourceNodeId = sourceNodeId;
    this.targetNodeId = targetNodeId;
  }

  getConnectedNodes(nodeRegistry) {
    return {
      sourceNode: nodeRegistry.get(this.sourceNodeId),
      targetNode: nodeRegistry.get(this.targetNodeId),
    };
  }
}

class ForceDirectedLayoutParameters {
  constructor(numberOfNodes, numberOfEdges) {
    this.numberOfNodes = numberOfNodes;
    this.numberOfEdges = numberOfEdges;
    this.calculateAdaptiveParameters();
  }

  calculateAdaptiveParameters() {
    // Graph analysis
    this.graphDensity =
      this.numberOfEdges /
      ((this.numberOfNodes * (this.numberOfNodes - 1)) / 2);
    this.averageNodeDegree = (2 * this.numberOfEdges) / this.numberOfNodes;

    // Fruchterman-Reingold inspired formulas
    this.layoutArea = 1.0; // Normalized 1x1 space
    this.optimalNodeDistance = Math.sqrt(this.layoutArea / this.numberOfNodes);

    // Simulation parameters
    this.maximumIterations = Math.max(500, 20 * this.numberOfNodes);
    this.idealSpringLength = this.optimalNodeDistance * 1.2;
    this.repulsionStrength =
      this.optimalNodeDistance * this.optimalNodeDistance * 0.8;
    this.attractionStrength = 0.3 * this.graphDensity;
    this.dampingCoefficient = 0.9 - this.graphDensity * 0.1;
    this.minimumNodeDistance = this.idealSpringLength * 0.15;
    this.edgeWeightMultiplier = Math.max(1.5, 3.0 - this.averageNodeDegree);

    // Post-processing parameters
    this.separationIterations = 100;
    this.separationThreshold = this.idealSpringLength * 1.5;
    this.separationForceStrength = 0.001;
  }

  logParameters() {
    console.log(
      `Graph Statistics: ${this.numberOfNodes} nodes, ${this.numberOfEdges} edges`
    );
    console.log(
      `Density: ${this.graphDensity.toFixed(
        3
      )}, Average Degree: ${this.averageNodeDegree.toFixed(1)}`
    );
    console.log(
      `Optimal Distance: ${this.optimalNodeDistance.toFixed(
        3
      )}, Spring Length: ${this.idealSpringLength.toFixed(3)}`
    );
    console.log(
      `Repulsion: ${this.repulsionStrength.toFixed(
        3
      )}, Attraction: ${this.attractionStrength.toFixed(3)}`
    );
  }
}

class ForceDirectedLayoutEngine {
  constructor(graphData) {
    this.nodeRegistry = new Map();
    this.edgeList = [];
    this.connectedNodePairs = new Set();

    this.initializeGraphStructure(graphData);
    this.layoutParameters = new ForceDirectedLayoutParameters(
      this.nodeRegistry.size,
      this.edgeList.length
    );
    this.layoutParameters.logParameters();
  }

  initializeGraphStructure(graphData) {
    // Create nodes
    graphData.nodes.forEach((nodeData) => {
      const graphNode = new GraphNode(nodeData.id, {
        x: nodeData.x,
        y: nodeData.y,
      });
      this.nodeRegistry.set(nodeData.id, graphNode);
    });

    // Create edges and establish connections
    graphData.edges.forEach(([sourceId, targetId]) => {
      const edge = new GraphEdge(sourceId, targetId);
      this.edgeList.push(edge);

      // Bidirectional connections
      this.nodeRegistry.get(sourceId).addConnection(targetId);
      this.nodeRegistry.get(targetId).addConnection(sourceId);

      // Track connected pairs for post-processing
      this.connectedNodePairs.add(`${sourceId}-${targetId}`);
      this.connectedNodePairs.add(`${targetId}-${sourceId}`);
    });
  }

  executeLayoutOptimization() {
    for (
      let currentIteration = 0;
      currentIteration < this.layoutParameters.maximumIterations;
      currentIteration++
    ) {
      const coolingTemperature =
        1.0 - currentIteration / this.layoutParameters.maximumIterations;
      const currentDampingFactor =
        this.layoutParameters.dampingCoefficient * coolingTemperature;

      this.resetAllForces();
      this.calculateRepulsiveForces();
      this.calculateAttractiveForces();
      this.updateAllNodePositions(currentDampingFactor, coolingTemperature);
    }

    this.performPostProcessingSeparation();
    this.normalizePositionsToUnitSquare();
  }

  resetAllForces() {
    this.nodeRegistry.forEach((node) => node.resetForces());
  }

  calculateRepulsiveForces() {
    const nodeArray = Array.from(this.nodeRegistry.values());

    for (let i = 0; i < nodeArray.length; i++) {
      for (let j = i + 1; j < nodeArray.length; j++) {
        const nodeA = nodeArray[i];
        const nodeB = nodeArray[j];

        this.applyRepulsionBetweenNodes(nodeA, nodeB);
      }
    }
  }

  applyRepulsionBetweenNodes(nodeA, nodeB) {
    let deltaX = nodeA.position.x - nodeB.position.x;
    let deltaY = nodeA.position.y - nodeB.position.y;
    let distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);

    // Handle overlapping nodes
    if (distance < this.layoutParameters.minimumNodeDistance) {
      distance = this.layoutParameters.minimumNodeDistance;
      // Add jitter to separate overlapping nodes
      deltaX += (Math.random() - 0.5) * 0.01;
      deltaY += (Math.random() - 0.5) * 0.01;
    }

    const repulsionMagnitude =
      this.layoutParameters.repulsionStrength / (distance * distance);
    const repulsionForceX = (deltaX / distance) * repulsionMagnitude;
    const repulsionForceY = (deltaY / distance) * repulsionMagnitude;

    nodeA.applyForce(new Vector2D(repulsionForceX, repulsionForceY));
    nodeB.applyForce(new Vector2D(-repulsionForceX, -repulsionForceY));
  }

  calculateAttractiveForces() {
    this.edgeList.forEach((edge) => {
      const { sourceNode, targetNode } = edge.getConnectedNodes(
        this.nodeRegistry
      );
      this.applyAttractionBetweenConnectedNodes(sourceNode, targetNode);
    });
  }

  applyAttractionBetweenConnectedNodes(sourceNode, targetNode) {
    const deltaX = sourceNode.position.x - targetNode.position.x;
    const deltaY = sourceNode.position.y - targetNode.position.y;
    let distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);

    if (distance < 0.001) distance = 0.001; // Prevent division by zero

    const springForce =
      this.layoutParameters.attractionStrength *
      this.layoutParameters.edgeWeightMultiplier *
      (distance - this.layoutParameters.idealSpringLength);

    const attractionForceX = (deltaX / distance) * springForce;
    const attractionForceY = (deltaY / distance) * springForce;

    sourceNode.applyForce(new Vector2D(-attractionForceX, -attractionForceY));
    targetNode.applyForce(new Vector2D(attractionForceX, attractionForceY));
  }

  updateAllNodePositions(dampingFactor, temperature) {
    const maximumForce = 0.1 * temperature;

    this.nodeRegistry.forEach((node) => {
      node.updatePosition(dampingFactor, maximumForce);
    });
  }

  performPostProcessingSeparation() {
    for (
      let iteration = 0;
      iteration < this.layoutParameters.separationIterations;
      iteration++
    ) {
      const nodeArray = Array.from(this.nodeRegistry.values());

      for (let i = 0; i < nodeArray.length; i++) {
        for (let j = i + 1; j < nodeArray.length; j++) {
          const nodeA = nodeArray[i];
          const nodeB = nodeArray[j];

          // Skip connected nodes
          if (this.connectedNodePairs.has(`${nodeA.nodeId}-${nodeB.nodeId}`))
            continue;

          this.separateUnconnectedNodesIfTooClose(nodeA, nodeB);
        }
      }
    }
  }

  separateUnconnectedNodesIfTooClose(nodeA, nodeB) {
    const deltaX = nodeA.position.x - nodeB.position.x;
    const deltaY = nodeA.position.y - nodeB.position.y;
    const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);

    if (distance < this.layoutParameters.separationThreshold) {
      const separationForceX =
        (deltaX / distance) * this.layoutParameters.separationForceStrength;
      const separationForceY =
        (deltaY / distance) * this.layoutParameters.separationForceStrength;

      nodeA.position.add(new Vector2D(separationForceX, separationForceY));
      nodeB.position.subtract(new Vector2D(separationForceX, separationForceY));
    }
  }

  normalizePositionsToUnitSquare() {
    const positions = Array.from(this.nodeRegistry.values()).map(
      (node) => node.position
    );

    const boundingBox = {
      minX: Math.min(...positions.map((pos) => pos.x)),
      maxX: Math.max(...positions.map((pos) => pos.x)),
      minY: Math.min(...positions.map((pos) => pos.y)),
      maxY: Math.max(...positions.map((pos) => pos.y)),
    };

    const rangeX = boundingBox.maxX - boundingBox.minX || 1;
    const rangeY = boundingBox.maxY - boundingBox.minY || 1;

    this.nodeRegistry.forEach((node) => {
      node.position.x = (node.position.x - boundingBox.minX) / rangeX;
      node.position.y = (node.position.y - boundingBox.minY) / rangeY;
    });
  }

  getOptimizedNodePositions() {
    const optimizedPositions = {};
    this.nodeRegistry.forEach((node, nodeId) => {
      optimizedPositions[nodeId] = {
        x: node.position.x,
        y: node.position.y,
      };
    });
    return optimizedPositions;
  }

  getOriginalNodePositions() {
    const originalPositions = {};
    this.nodeRegistry.forEach((node, nodeId) => {
      originalPositions[nodeId] = {
        x: node.originalPosition.x,
        y: node.originalPosition.y,
      };
    });
    return originalPositions;
  }
}

class GraphVisualizationRenderer {
  constructor(canvasElementId, graphData, layoutEngine) {
    this.canvasElement = document.getElementById(canvasElementId);
    this.renderingContext = this.canvasElement.getContext("2d");
    this.canvasSize = this.canvasElement.width;
    this.edgeList = graphData.edges;
    this.layoutEngine = layoutEngine;

    // Visual styling parameters
    this.visualPadding = 30;
    this.drawableArea = this.canvasSize - 2 * this.visualPadding;
    this.nodeRadius = 3;
    this.nodeFillColor = "blue";
    this.edgeStrokeColor = "#ccc";
    this.labelFillColor = "black";
    this.labelFont = "8px Arial";
    this.labelOffset = 5;
  }

  renderGraphVisualization(nodePositionData, visualizationTitle) {
    this.clearCanvas();
    this.drawAllEdges(nodePositionData);
    this.drawAllNodesWithLabels(nodePositionData);
  }

  clearCanvas() {
    this.renderingContext.clearRect(0, 0, this.canvasSize, this.canvasSize);
  }

  drawAllEdges(nodePositionData) {
    this.renderingContext.strokeStyle = this.edgeStrokeColor;
    this.renderingContext.lineWidth = 1;

    this.edgeList.forEach(([sourceNodeId, targetNodeId]) => {
      const sourcePosition = this.calculateScreenPosition(
        nodePositionData[sourceNodeId]
      );
      const targetPosition = this.calculateScreenPosition(
        nodePositionData[targetNodeId]
      );

      this.renderingContext.beginPath();
      this.renderingContext.moveTo(sourcePosition.x, sourcePosition.y);
      this.renderingContext.lineTo(targetPosition.x, targetPosition.y);
      this.renderingContext.stroke();
    });
  }

  drawAllNodesWithLabels(nodePositionData) {
    this.renderingContext.fillStyle = this.nodeFillColor;
    this.renderingContext.font = this.labelFont;
    this.renderingContext.textAlign = "left";
    this.renderingContext.textBaseline = "middle";

    Object.entries(nodePositionData).forEach(([nodeId, position]) => {
      const screenPosition = this.calculateScreenPosition(position);

      this.drawNodeCircle(screenPosition);
      this.drawNodeLabel(nodeId, screenPosition);
    });
  }

  drawNodeCircle(screenPosition) {
    this.renderingContext.fillStyle = this.nodeFillColor;
    this.renderingContext.beginPath();
    this.renderingContext.arc(
      screenPosition.x,
      screenPosition.y,
      this.nodeRadius,
      0,
      2 * Math.PI
    );
    this.renderingContext.fill();
  }

  drawNodeLabel(nodeId, screenPosition) {
    this.renderingContext.fillStyle = this.labelFillColor;

    const labelX = screenPosition.x + this.labelOffset;
    const labelY = screenPosition.y;

    // Ensure label stays within canvas bounds
    const labelWidth = this.renderingContext.measureText(nodeId).width;
    const finalLabelX = Math.min(labelX, this.canvasSize - labelWidth - 5);

    this.renderingContext.fillText(nodeId, finalLabelX, labelY);
  }

  calculateScreenPosition(normalizedPosition) {
    return {
      x: normalizedPosition.x * this.drawableArea + this.visualPadding,
      y: normalizedPosition.y * this.drawableArea + this.visualPadding,
    };
  }
}

class GraphVisualizationApplication {
  constructor(graphData) {
    this.graphData = graphData;
    this.layoutEngine = new ForceDirectedLayoutEngine(graphData);

    // Initialize renderers
    this.originalDataRenderer = new GraphVisualizationRenderer(
      "originalCanvas",
      graphData,
      this.layoutEngine
    );
    this.optimizedLayoutRenderer = new GraphVisualizationRenderer(
      "graphCanvas",
      graphData,
      this.layoutEngine
    );
  }

  executeVisualization() {
    // Get original positions
    const originalNodePositions = this.layoutEngine.getOriginalNodePositions();

    // Execute layout optimization
    console.log("Starting force-directed layout optimization...");
    this.layoutEngine.executeLayoutOptimization();

    // Get optimized positions
    const optimizedNodePositions =
      this.layoutEngine.getOptimizedNodePositions();

    // Render both visualizations
    this.originalDataRenderer.renderGraphVisualization(
      originalNodePositions,
      "Original Data"
    );
    this.optimizedLayoutRenderer.renderGraphVisualization(
      optimizedNodePositions,
      "Optimized Layout"
    );

    console.log("Visualization complete!");
    console.log("Original positions:", originalNodePositions);
    console.log("Optimized positions:", optimizedNodePositions);
  }
}
