<html>
  <body>
    <div style="display: flex; gap: 20px; align-items: flex-start">
      <div>
        <h3>Original Data Positions</h3>
        <canvas
          id="originalCanvas"
          width="400"
          height="400"
          style="border: 1px solid #000"
        ></canvas>
      </div>
      <div>
        <h3>Optimized Layout</h3>
        <canvas
          id="graphCanvas"
          width="400"
          height="400"
          style="border: 1px solid #000"
        ></canvas>
      </div>
    </div>

    <script>
      // Provided data
      const graphData = {
        nodes: [
          { id: "Blantyre", x: 0.9134213014976535, y: 0.2540740323898225 },
          { id: "Chikwawa", x: 0.14374226893980302, y: 0.3910154112946962 },
          { id: "Chiradzulu", x: 0.9351749046225152, y: 0.5027042682331085 },
          { id: "Chi<PERSON>a", x: 0.5033532302137712, y: 0.6371050642113303 },
          { id: "Dedza", x: 0.32675593364689126, y: 0.32741458873737384 },
          { id: "Dowa", x: 0.44893854232683894, y: 0.3534310438093927 },
          { id: "Karonga", x: 0.7719114930591756, y: 0.7164846847486838 },
          { id: "Kasungu", x: 0.9486271739760203, y: 0.03717616769235954 },
          { id: "Lilongwe", x: 0.03185092819745572, y: 0.07907784991666855 },
          { id: "Machinga", x: 0.4976553188158377, y: 0.15957191749775634 },
          { id: "Mangochi", x: 0.2417748469656349, y: 0.22132470346325728 },
          { id: "Mchinji", x: 0.8029651384628501, y: 0.4170419722297135 },
          { id: "Mulanje", x: 0.6998851394303303, y: 0.7300336822154281 },
          { id: "Mwanza", x: 0.3093976112949879, y: 0.9141857772478698 },
          { id: "Mzimba", x: 0.16190201617155997, y: 0.8356366262711726 },
          { id: "Neno", x: 0.9869012833729535, y: 0.3511167097222222 },
          { id: "Nkhata Bay", x: 0.0882233026546202, y: 0.18674223158715342 },
          {
            id: "Nkhotakota",
            x: 0.17467106409589772,
            y: 0.0010883823237957113,
          },
          { id: "Nsanje", x: 0.8093914854184416, y: 0.5079865816371467 },
          { id: "Ntcheu", x: 0.8588177668360885, y: 0.4167540312634731 },
          { id: "Ntchisi", x: 0.3969781197576786, y: 0.9982702660465445 },
          { id: "Phalombe", x: 0.934352810085411, y: 0.7328019939159007 },
          { id: "Rumphi", x: 0.2438492080065875, y: 0.0387865957339274 },
          { id: "Salima", x: 0.837201462046805, y: 0.9965726289086905 },
          { id: "Thyolo", x: 0.6272655175304893, y: 0.7688215502317457 },
          { id: "Zomba", x: 0.7252659639019722, y: 0.810888016094619 },
          { id: "Balaka", x: 0.15932838570160823, y: 0.5698123530031478 },
          { id: "Likoma", x: 0.3488343806746971, y: 0.6253864059894712 },
        ],
        edges: [
          ["Blantyre", "Chikwawa"],
          ["Blantyre", "Chiradzulu"],
          ["Blantyre", "Thyolo"],
          ["Chikwawa", "Nsanje"],
          ["Chikwawa", "Mwanza"],
          ["Chiradzulu", "Zomba"],
          ["Chiradzulu", "Phalombe"],
          ["Chitipa", "Karonga"],
          ["Dedza", "Lilongwe"],
          ["Dedza", "Ntcheu"],
          ["Dowa", "Lilongwe"],
          ["Dowa", "Ntchisi"],
          ["Karonga", "Rumphi"],
          ["Kasungu", "Lilongwe"],
          ["Kasungu", "Mzimba"],
          ["Lilongwe", "Mchinji"],
          ["Lilongwe", "Salima"],
          ["Machinga", "Zomba"],
          ["Machinga", "Balaka"],
          ["Mangochi", "Balaka"],
          ["Mangochi", "Salima"],
          ["Mulanje", "Phalombe"],
          ["Mulanje", "Thyolo"],
          ["Mwanza", "Neno"],
          ["Mzimba", "Nkhata Bay"],
          ["Mzimba", "Rumphi"],
          ["Nkhata Bay", "Nkhotakota"],
          ["Nkhotakota", "Salima"],
          ["Nsanje", "Chikwawa"],
          ["Ntcheu", "Balaka"],
          ["Ntchisi", "Nkhotakota"],
          ["Phalombe", "Mulanje"],
          ["Salima", "Nkhotakota"],
          ["Zomba", "Machinga"],
        ],
      };

      // Store original positions for comparison
      const originalNodes = graphData.nodes.reduce((acc, node) => {
        acc[node.id] = { x: node.x, y: node.y };
        return acc;
      }, {});

      const nodes = graphData.nodes.reduce((acc, node) => {
        acc[node.id] = { x: node.x, y: node.y };
        return acc;
      }, {});
      const edges = graphData.edges;

      // Improved Parameters
      const iterations = 500;
      const repulsion = 0.05; // Increased repulsion to spread nodes apart
      const attraction = 0.01; // Increased attraction for better edge lengths
      const springLength = 0.15; // Longer spring length for more spacing
      const damping = 0.9; // Damping factor to stabilize movement
      const minDistance = 0.02; // Minimum distance between nodes

      // Improved Simulation loop with adaptive cooling
      for (let i = 0; i < iterations; i++) {
        const forces = {};
        const temperature = 1.0 - i / iterations; // Cooling schedule
        const currentDamping = damping * temperature;

        // Initialize forces
        for (let a in nodes) {
          forces[a] = { x: 0, y: 0 };
        }

        // Calculate repulsive forces (all pairs)
        for (let a in nodes) {
          for (let b in nodes) {
            if (a === b) continue;

            let dx = nodes[a].x - nodes[b].x;
            let dy = nodes[a].y - nodes[b].y;
            let dist = Math.sqrt(dx * dx + dy * dy);

            // Prevent division by zero and ensure minimum distance
            if (dist < minDistance) {
              dist = minDistance;
              // Add random jitter to separate overlapping nodes
              dx += (Math.random() - 0.5) * 0.01;
              dy += (Math.random() - 0.5) * 0.01;
            }

            // Stronger repulsion for closer nodes
            let repForce = repulsion / (dist * dist);
            forces[a].x += (dx / dist) * repForce;
            forces[a].y += (dy / dist) * repForce;
          }
        }

        // Calculate attractive forces (connected nodes only)
        for (let [a, b] of edges) {
          let dx = nodes[a].x - nodes[b].x;
          let dy = nodes[a].y - nodes[b].y;
          let dist = Math.sqrt(dx * dx + dy * dy);

          if (dist < 0.001) dist = 0.001; // Prevent division by zero

          // Spring force proportional to distance from ideal length
          let attrForce = attraction * (dist - springLength);
          let fx = (dx / dist) * attrForce;
          let fy = (dy / dist) * attrForce;

          forces[a].x -= fx;
          forces[a].y -= fy;
          forces[b].x += fx;
          forces[b].y += fy;
        }

        // Apply forces with damping and temperature
        for (let node in nodes) {
          // Limit force magnitude to prevent instability
          const forceX = forces[node].x * currentDamping;
          const forceY = forces[node].y * currentDamping;
          const forceMag = Math.sqrt(forceX * forceX + forceY * forceY);
          const maxForce = 0.1 * temperature; // Reduce max force over time

          if (forceMag > maxForce) {
            const scale = maxForce / forceMag;
            nodes[node].x += forceX * scale;
            nodes[node].y += forceY * scale;
          } else {
            nodes[node].x += forceX;
            nodes[node].y += forceY;
          }
        }
      }

      // Normalize to 1x1 square with proper bounds
      let maxX = Math.max(...Object.values(nodes).map((n) => n.x));
      let maxY = Math.max(...Object.values(nodes).map((n) => n.y));
      let minX = Math.min(...Object.values(nodes).map((n) => n.x));
      let minY = Math.min(...Object.values(nodes).map((n) => n.y));

      // Ensure we have valid ranges
      const rangeX = maxX - minX || 1;
      const rangeY = maxY - minY || 1;

      for (let node in nodes) {
        nodes[node].x = (nodes[node].x - minX) / rangeX;
        nodes[node].y = (nodes[node].y - minY) / rangeY;
      }

      console.log("Optimized positions:", nodes);

      // === Function to draw graph ===
      function drawGraph(canvasId, nodeData, title) {
        const canvas = document.getElementById(canvasId);
        const ctx = canvas.getContext("2d");
        const size = canvas.width;
        const padding = 30; // Padding to ensure labels don't get cut off
        const drawSize = size - 2 * padding;

        // Clear canvas
        ctx.clearRect(0, 0, size, size);

        // Draw edges
        ctx.strokeStyle = "#ccc";
        ctx.lineWidth = 1;
        edges.forEach(([a, b]) => {
          ctx.beginPath();
          ctx.moveTo(
            nodeData[a].x * drawSize + padding,
            nodeData[a].y * drawSize + padding
          );
          ctx.lineTo(
            nodeData[b].x * drawSize + padding,
            nodeData[b].y * drawSize + padding
          );
          ctx.stroke();
        });

        // Draw nodes and labels
        ctx.fillStyle = "blue";
        ctx.font = "8px Arial";
        ctx.textAlign = "left";
        ctx.textBaseline = "middle";

        for (let node in nodeData) {
          const x = nodeData[node].x * drawSize + padding;
          const y = nodeData[node].y * drawSize + padding;

          // Draw node circle
          ctx.beginPath();
          ctx.arc(x, y, 3, 0, 2 * Math.PI);
          ctx.fill();

          // Draw label with smart positioning to avoid overlap
          ctx.fillStyle = "black";
          const labelX = x + 5;
          const labelY = y;

          // Ensure label stays within canvas bounds
          const textWidth = ctx.measureText(node).width;
          const finalLabelX = Math.min(labelX, size - textWidth - 5);

          ctx.fillText(node, finalLabelX, labelY);
          ctx.fillStyle = "blue";
        }
      }

      // === Render Original Data ===
      drawGraph("originalCanvas", originalNodes, "Original");

      // === Render Optimized Layout ===
      drawGraph("graphCanvas", nodes, "Optimized");
    </script>
  </body>
</html>
