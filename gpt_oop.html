<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OOP Force-Directed Graph Visualization</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        
        .visualization-container {
            display: flex;
            gap: 20px;
            align-items: flex-start;
            justify-content: center;
            flex-wrap: wrap;
        }
        
        .canvas-section {
            background: white;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .canvas-section h3 {
            margin: 0 0 10px 0;
            color: #333;
            text-align: center;
        }
        
        canvas {
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        
        .controls {
            text-align: center;
            margin-bottom: 20px;
        }
        
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        
        button:hover {
            background-color: #0056b3;
        }
        
        .info-panel {
            max-width: 800px;
            margin: 20px auto;
            background: white;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <h1 style="text-align: center; color: #333;">Force-Directed Graph Layout Comparison</h1>
    
    <div class="controls">
        <button onclick="runVisualization()">Run Optimization</button>
        <button onclick="resetVisualization()">Reset</button>
    </div>
    
    <div class="visualization-container">
        <div class="canvas-section">
            <h3>Original Data Positions</h3>
            <canvas id="originalCanvas" width="400" height="400"></canvas>
        </div>
        <div class="canvas-section">
            <h3>Optimized Layout</h3>
            <canvas id="graphCanvas" width="400" height="400"></canvas>
        </div>
    </div>
    
    <div class="info-panel">
        <h3>Algorithm Information</h3>
        <p><strong>Implementation:</strong> Object-Oriented Force-Directed Layout using Fruchterman-Reingold algorithm</p>
        <p><strong>Features:</strong> Adaptive parameters, collision detection, post-processing separation</p>
        <p><strong>Classes:</strong> Vector2D, GraphNode, GraphEdge, ForceDirectedLayoutParameters, ForceDirectedLayoutEngine, GraphVisualizationRenderer, GraphVisualizationApplication</p>
        <div id="algorithmStats"></div>
    </div>

    <!-- Include the OOP implementation -->
    <script src="ForceDirectedGraph.js"></script>
    
    <script>
        // Malawi districts graph data
        const malawiDistrictsGraphData = {
            nodes: [
                { id: "Blantyre", x: 0.9134213014976535, y: 0.2540740323898225 },
                { id: "Chikwawa", x: 0.14374226893980302, y: 0.3910154112946962 },
                { id: "Chiradzulu", x: 0.9351749046225152, y: 0.5027042682331085 },
                { id: "Chitipa", x: 0.5033532302137712, y: 0.6371050642113303 },
                { id: "Dedza", x: 0.32675593364689126, y: 0.32741458873737384 },
                { id: "Dowa", x: 0.44893854232683894, y: 0.3534310438093927 },
                { id: "Karonga", x: 0.7719114930591756, y: 0.7164846847486838 },
                { id: "Kasungu", x: 0.9486271739760203, y: 0.03717616769235954 },
                { id: "Lilongwe", x: 0.03185092819745572, y: 0.07907784991666855 },
                { id: "Machinga", x: 0.4976553188158377, y: 0.15957191749775634 },
                { id: "Mangochi", x: 0.2417748469656349, y: 0.22132470346325728 },
                { id: "Mchinji", x: 0.8029651384628501, y: 0.4170419722297135 },
                { id: "Mulanje", x: 0.6998851394303303, y: 0.7300336822154281 },
                { id: "Mwanza", x: 0.3093976112949879, y: 0.9141857772478698 },
                { id: "Mzimba", x: 0.16190201617155997, y: 0.8356366262711726 },
                { id: "Neno", x: 0.9869012833729535, y: 0.3511167097222222 },
                { id: "Nkhata Bay", x: 0.0882233026546202, y: 0.18674223158715342 },
                { id: "Nkhotakota", x: 0.17467106409589772, y: 0.0010883823237957113 },
                { id: "Nsanje", x: 0.8093914854184416, y: 0.5079865816371467 },
                { id: "Ntcheu", x: 0.8588177668360885, y: 0.4167540312634731 },
                { id: "Ntchisi", x: 0.3969781197576786, y: 0.9982702660465445 },
                { id: "Phalombe", x: 0.934352810085411, y: 0.7328019939159007 },
                { id: "Rumphi", x: 0.2438492080065875, y: 0.0387865957339274 },
                { id: "Salima", x: 0.837201462046805, y: 0.9965726289086905 },
                { id: "Thyolo", x: 0.6272655175304893, y: 0.7688215502317457 },
                { id: "Zomba", x: 0.7252659639019722, y: 0.810888016094619 },
                { id: "Balaka", x: 0.15932838570160823, y: 0.5698123530031478 },
                { id: "Likoma", x: 0.3488343806746971, y: 0.6253864059894712 }
            ],
            edges: [
                ["Blantyre", "Chikwawa"], ["Blantyre", "Chiradzulu"], ["Blantyre", "Thyolo"],
                ["Chikwawa", "Nsanje"], ["Chikwawa", "Mwanza"], ["Chiradzulu", "Zomba"],
                ["Chiradzulu", "Phalombe"], ["Chitipa", "Karonga"], ["Dedza", "Lilongwe"],
                ["Dedza", "Ntcheu"], ["Dowa", "Lilongwe"], ["Dowa", "Ntchisi"],
                ["Karonga", "Rumphi"], ["Kasungu", "Lilongwe"], ["Kasungu", "Mzimba"],
                ["Lilongwe", "Mchinji"], ["Lilongwe", "Salima"], ["Machinga", "Zomba"],
                ["Machinga", "Balaka"], ["Mangochi", "Balaka"], ["Mangochi", "Salima"],
                ["Mulanje", "Phalombe"], ["Mulanje", "Thyolo"], ["Mwanza", "Neno"],
                ["Mzimba", "Nkhata Bay"], ["Mzimba", "Rumphi"], ["Nkhata Bay", "Nkhotakota"],
                ["Nkhotakota", "Salima"], ["Nsanje", "Chikwawa"], ["Ntcheu", "Balaka"],
                ["Ntchisi", "Nkhotakota"], ["Phalombe", "Mulanje"], ["Salima", "Nkhotakota"],
                ["Zomba", "Machinga"]
            ]
        };

        // Global application instance
        let graphVisualizationApp = null;

        function runVisualization() {
            console.log("Initializing OOP Force-Directed Graph Visualization...");
            
            // Create new application instance
            graphVisualizationApp = new GraphVisualizationApplication(malawiDistrictsGraphData);
            
            // Execute the visualization
            graphVisualizationApp.executeVisualization();
            
            // Update info panel
            updateAlgorithmStats();
        }

        function resetVisualization() {
            // Clear canvases
            const originalCanvas = document.getElementById('originalCanvas');
            const optimizedCanvas = document.getElementById('graphCanvas');
            
            const originalCtx = originalCanvas.getContext('2d');
            const optimizedCtx = optimizedCanvas.getContext('2d');
            
            originalCtx.clearRect(0, 0, originalCanvas.width, originalCanvas.height);
            optimizedCtx.clearRect(0, 0, optimizedCanvas.width, optimizedCanvas.height);
            
            // Clear stats
            document.getElementById('algorithmStats').innerHTML = '';
            
            console.log("Visualization reset");
        }

        function updateAlgorithmStats() {
            if (!graphVisualizationApp) return;
            
            const params = graphVisualizationApp.layoutEngine.layoutParameters;
            const statsHtml = `
                <h4>Computed Parameters:</h4>
                <ul>
                    <li><strong>Nodes:</strong> ${params.numberOfNodes}</li>
                    <li><strong>Edges:</strong> ${params.numberOfEdges}</li>
                    <li><strong>Graph Density:</strong> ${params.graphDensity.toFixed(3)}</li>
                    <li><strong>Average Degree:</strong> ${params.averageNodeDegree.toFixed(1)}</li>
                    <li><strong>Optimal Distance:</strong> ${params.optimalNodeDistance.toFixed(3)}</li>
                    <li><strong>Spring Length:</strong> ${params.idealSpringLength.toFixed(3)}</li>
                    <li><strong>Repulsion Strength:</strong> ${params.repulsionStrength.toFixed(3)}</li>
                    <li><strong>Attraction Strength:</strong> ${params.attractionStrength.toFixed(3)}</li>
                    <li><strong>Iterations:</strong> ${params.maximumIterations}</li>
                </ul>
            `;
            document.getElementById('algorithmStats').innerHTML = statsHtml;
        }

        // Initialize with original data on page load
        window.addEventListener('load', function() {
            // Show original data immediately
            const originalRenderer = new GraphVisualizationRenderer(
                "originalCanvas", 
                malawiDistrictsGraphData, 
                null
            );
            
            // Convert original data to expected format
            const originalPositions = {};
            malawiDistrictsGraphData.nodes.forEach(node => {
                originalPositions[node.id] = { x: node.x, y: node.y };
            });
            
            originalRenderer.renderGraphVisualization(originalPositions, "Original Data");
            
            console.log("Page loaded. Click 'Run Optimization' to see the improved layout!");
        });
    </script>
</body>
</html>
